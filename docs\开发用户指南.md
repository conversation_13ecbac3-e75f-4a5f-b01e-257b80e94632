# 开发用户指南 (Development User Guidelines)

## 核心原则

### 1. 语言要求
- **必须使用中文回复所有用户问题**
- 无论用户使用何种语言提问，都必须用中文回复
- 代码注释必须使用中文
- 文档和说明必须使用中文编写

### 2. 项目理解
- 开启新对话时，首先阅读项目 `docs/` 目录下的说明文档（.md格式）
- 充分理解项目情况和当前开发进度
- 连续对话时可直接针对问题进行回答

### 3. 系统环境
- 系统版本：Windows 10 64位
- 默认使用 cmd 进行命令行操作
- 使用 Windows 命令进行操作
- 命令行操作前必须确认在正确的虚拟环境下

## 开发规范

### 代码编写
1. **注释规范**
   - 所有注释必须使用中文
   - 注释要清晰说明代码功能和逻辑
   - 复杂算法需要详细注释

2. **修改原则**
   - 除了明确要求修改的部分，其他内容不要修改
   - 保持代码风格一致性
   - 遵循项目现有的架构模式

3. **质量保证**
   - 重大修改后需按照"检查用例"进行全面检查
   - 建议编写或更新测试用例
   - 执行测试确保修改正确性

### 问题处理
1. **问题定位**
   - 出现bug时，先定位问题所在
   - 分析错误原因和影响范围
   - 制定修复方案

2. **回答规范**
   - 回答问题时不包含无关信息
   - 专注于用户的具体需求
   - 提供准确、简洁的解决方案

### 操作规范
1. **权限控制**
   - 不执行非指令操作
   - 重大操作前需要用户确认
   - 遵循最小权限原则

2. **环境管理**
   - 使用包管理器管理依赖
   - 不直接编辑配置文件
   - 保持开发环境整洁

## 工作流程

### 信息收集
1. 使用代码库检索工具获取相关信息
2. 理解现有代码结构和逻辑
3. 确认修改范围和影响

### 任务规划
1. 对复杂任务进行分解
2. 制定详细的执行计划
3. 使用任务管理工具跟踪进度

### 代码修改
1. 使用 str-replace-editor 工具进行编辑
2. 保持代码风格一致
3. 添加必要的中文注释

### 测试验证
1. 编写或更新测试用例
2. 执行测试确保功能正确
3. 验证修改不影响其他功能

## 沟通规范

### 用户交互
- 始终使用中文与用户沟通
- 提供清晰、准确的回答
- 主动询问不明确的需求

### 进度汇报
- 及时汇报工作进度
- 说明遇到的问题和解决方案
- 提供下一步工作建议

### 文档维护
- 更新相关文档
- 记录重要的修改和决策
- 保持文档的时效性

## 质量标准

### 代码质量
- 代码逻辑清晰
- 注释完整准确
- 符合项目规范

### 功能质量
- 满足用户需求
- 功能稳定可靠
- 性能符合要求

### 文档质量
- 内容准确完整
- 格式规范统一
- 易于理解和维护

## 注意事项

1. **安全考虑**
   - 不执行可能损害系统的操作
   - 保护敏感信息
   - 遵循安全最佳实践

2. **兼容性**
   - 考虑不同环境的兼容性
   - 测试多种使用场景
   - 确保向后兼容

3. **可维护性**
   - 编写易于维护的代码
   - 提供清晰的文档
   - 考虑未来的扩展需求

---

**重要提醒：所有开发活动都必须使用中文进行沟通和文档编写，确保团队成员能够有效协作和知识传承。**
