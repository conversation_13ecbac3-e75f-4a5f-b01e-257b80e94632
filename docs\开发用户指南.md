# 开发用户指南 (Development User Guidelines)

## 核心原则

### 1. 语言要求（必须严格遵守）
- **请始终使用中文回答所有问题**
- **无论用户使用何种语言提问，都请用中文回复**
- 代码注释必须使用中文
- 文档和说明必须使用中文编写
- 所有交流和沟通都必须使用中文

### 2. 项目理解和对话管理
- **开启新对话时，请首先阅读项目docs目录下有关项目和产品的说明文档（.md格式）**
- **充分理解该项目的情况，以及当前的开发进度，对问题进行回答**
- **连续对话时，可直接针对问题进行回答**
- 确保对项目背景和技术栈有全面了解

### 3. 系统环境和命令行操作
- **系统版本为：Windows10 64位，请使用Windows命令进行操作**
- **默认使用cmd进行命令行操作**
- **命令行操作前必须确认在正确的虚拟环境下操作**
- 熟悉Windows环境下的开发工具和命令

## 开发规范

### 代码编写
1. **注释规范**
   - **请合理编写注释，注释必须使用中文**
   - 注释要清晰说明代码功能和逻辑
   - 复杂算法需要详细注释
   - 确保注释的准确性和时效性

2. **修改原则**
   - **除了我要求修改的部分，其他内容请不要修改**
   - 保持代码风格一致性
   - 遵循项目现有的架构模式
   - 严格按照用户指令进行修改

3. **质量保证**
   - **做出重大修改后需按照"检查用例"对代码做全面的检查**
   - 建议编写或更新测试用例
   - 执行测试确保修改正确性
   - 验证修改不会影响现有功能

### 问题处理
1. **问题定位和修复**
   - **出现bug时，请先定位到问题所在，然后进行修复**
   - 分析错误原因和影响范围
   - 制定系统性的修复方案
   - 确保修复的彻底性和有效性

2. **回答规范**
   - **请在回答问题时，不要包含任何与问题无关的信息，也不要修改代码**
   - 专注于用户的具体需求
   - 提供准确、简洁的解决方案
   - 避免冗余和无关信息

### 操作规范
1. **权限控制和指令执行**
   - **不要执行非指令操作**
   - 严格按照用户指令执行任务
   - 重大操作前需要用户确认
   - 遵循最小权限原则
   - 避免超出用户要求的范围

2. **环境管理**
   - 使用包管理器管理依赖
   - 不直接编辑配置文件
   - 保持开发环境整洁
   - 确保虚拟环境的正确性

## 工作流程

### 信息收集
1. 使用代码库检索工具获取相关信息
2. 理解现有代码结构和逻辑
3. 确认修改范围和影响

### 任务规划
1. 对复杂任务进行分解
2. 制定详细的执行计划
3. 使用任务管理工具跟踪进度

### 代码修改
1. 使用 str-replace-editor 工具进行编辑
2. 保持代码风格一致
3. 添加必要的中文注释

### 测试验证
1. 编写或更新测试用例
2. 执行测试确保功能正确
3. 验证修改不影响其他功能

## 沟通规范

### 用户交互
- 始终使用中文与用户沟通
- 提供清晰、准确的回答
- 主动询问不明确的需求

### 进度汇报
- 及时汇报工作进度
- 说明遇到的问题和解决方案
- 提供下一步工作建议

### 文档维护
- 更新相关文档
- 记录重要的修改和决策
- 保持文档的时效性

## 质量标准

### 代码质量
- 代码逻辑清晰
- 注释完整准确
- 符合项目规范

### 功能质量
- 满足用户需求
- 功能稳定可靠
- 性能符合要求

### 文档质量
- 内容准确完整
- 格式规范统一
- 易于理解和维护

## 注意事项

1. **安全考虑**
   - 不执行可能损害系统的操作
   - 保护敏感信息
   - 遵循安全最佳实践

2. **兼容性**
   - 考虑不同环境的兼容性
   - 测试多种使用场景
   - 确保向后兼容

3. **可维护性**
   - 编写易于维护的代码
   - 提供清晰的文档
   - 考虑未来的扩展需求

## 重要规则总结

以下是必须严格遵守的核心规则：

1. **请始终使用中文回答所有问题。无论用户使用何种语言提问，都请用中文回复。**

2. **开启新对话时，请首先阅读项目docs目录下有关项目和产品的说明文档（.md格式），充分理解该项目的情况，以及当前的开发进度，对问题进行回答。连续对话时，可直接针对问题进行回答。**

3. **系统版本为：Windows10 64位，请使用Windows命令进行操作。**

4. **默认使用cmd进行命令行操作。**

5. **命令行操作前必须确认在正确的虚拟环境下操作。**

6. **请合理编写注释，注释必须使用中文。**

7. **除了我要求修改的部分，其他内容请不要修改。**

8. **做出重大修改后需按照"检查用例"对代码做全面的检查。**

9. **不要执行非指令操作。**

10. **请在回答问题时，不要包含任何与问题无关的信息，也不要修改代码。**

11. **出现bug时，请先定位到问题所在，然后进行修复。**

---

**重要提醒：所有开发活动都必须使用中文进行沟通和文档编写，严格按照上述规则执行，确保开发工作的规范性和一致性。**
