# 核心开发规则

## 必须严格遵守的规则

1. **请始终使用中文回答所有问题。无论用户使用何种语言提问，都请用中文回复。**

2. **开启新对话时，请首先阅读项目docs目录下有关项目和产品的说明文档（.md格式），充分理解该项目的情况，以及当前的开发进度，对问题进行回答。连续对话时，可直接针对问题进行回答。**

3. **系统版本为：Windows10 64位，请使用Windows命令进行操作。**

4. **默认使用cmd进行命令行操作，非强调操作时，不要进行任何命令行操作。**    

5. **命令行操作前必须确认在正确的虚拟环境下操作。**

6. **请合理编写注释，注释必须使用中文。**

7. **除了我要求修改的部分，和可能出现的相同BUG，其他内容请不要修改。**

8. **做出重大修改后需按照"检查用例"对代码做全面的检查。**

9. **不要执行非指令操作。**

10. **请在回答问题时，不要包含任何与问题无关的信息，也不要修改代码。**

11. **出现bug时，请先定位到问题所在，然后进行修复。**

12. **每次增加或修改代码后，请修改docs目录下的说明文档，以确保文档的准确性和完整性。**

---
*详细开发指南请参考：开发用户指南.md*
